#!/usr/bin/env python3
"""
Latency Measurement Script for LiveKit + Pipecat Demo

This script measures mouth-to-ear latency by:
1. Connecting to the LiveKit room as a test client
2. Playing a beep sound
3. Measuring the time until the agent's echo response is received
4. Logging latency measurements to terminal and file

Requirements:
- Agent must be running and connected to the room
- LiveKit server must be running
- Microphone and speaker access required
"""

import asyncio
import time
import logging
import json
import statistics
from datetime import datetime
import numpy as np
import sounddevice as sd
from livekit import api, rtc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('latency_measurements.log')
    ]
)
logger = logging.getLogger(__name__)

class LatencyMeasurement:
    def __init__(self, room_url="ws://localhost:7880", room_name="pipecat-demo"):
        self.room_url = room_url
        self.room_name = room_name
        self.room = None
        self.measurements = []
        self.test_count = 0
        self.max_tests = 10
        
        # Audio settings
        self.sample_rate = 48000
        self.beep_duration = 0.5  # seconds
        self.beep_frequency = 1000  # Hz
        
        # Timing
        self.beep_start_time = None
        self.response_received_time = None
        
    def generate_beep(self):
        """Generate a beep sound for testing"""
        t = np.linspace(0, self.beep_duration, int(self.sample_rate * self.beep_duration))
        beep = 0.3 * np.sin(2 * np.pi * self.beep_frequency * t)
        return beep.astype(np.float32)
    
    async def connect_to_room(self):
        """Connect to the LiveKit room"""
        try:
            # Generate access token
            token = api.AccessToken() \
                .with_identity(f"LatencyTester-{int(time.time())}") \
                .with_name("Latency Measurement Client") \
                .with_grants(api.VideoGrants(
                    room_join=True,
                    room=self.room_name,
                    can_publish=True,
                    can_subscribe=True
                )) \
                .to_jwt()
            
            # Connect to room
            self.room = rtc.Room()
            
            # Set up event handlers
            self.room.on("participant_connected", self.on_participant_connected)
            self.room.on("track_subscribed", self.on_track_subscribed)
            self.room.on("track_unsubscribed", self.on_track_unsubscribed)
            
            await self.room.connect(self.room_url, token)
            logger.info(f"✅ Connected to room: {self.room_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to room: {e}")
            return False
    
    def on_participant_connected(self, participant):
        """Handle participant connection"""
        logger.info(f"👤 Participant connected: {participant.identity}")
    
    def on_track_subscribed(self, track, publication, participant):
        """Handle track subscription - this is where we detect agent response"""
        if track.kind == rtc.TrackKind.KIND_AUDIO and participant.identity.startswith("PipecatAgent"):
            logger.info(f"🎵 Subscribed to agent audio track from {participant.identity}")
            
            # Set up audio monitoring for response detection
            self.setup_response_detection(track)
    
    def on_track_unsubscribed(self, track, publication, participant):
        """Handle track unsubscription"""
        logger.info(f"🔇 Unsubscribed from {track.kind} track from {participant.identity}")
    
    def setup_response_detection(self, track):
        """Set up audio monitoring to detect agent response"""
        # This would require implementing audio analysis
        # For now, we'll use a simpler approach with timing
        pass
    
    async def play_beep_and_measure(self):
        """Play a beep and measure latency to agent response"""
        if not self.room:
            logger.error("❌ Not connected to room")
            return None
        
        try:
            # Generate beep audio
            beep_audio = self.generate_beep()
            
            # Create audio track
            audio_source = rtc.AudioSource(self.sample_rate, 1)
            track = rtc.LocalAudioTrack.create_audio_track("beep", audio_source)
            
            # Publish track
            publication = await self.room.local_participant.publish_track(track)
            logger.info("🔊 Published beep audio track")
            
            # Record start time
            self.beep_start_time = time.time()
            logger.info(f"⏱️  Beep started at: {self.beep_start_time}")
            
            # Push audio data
            audio_frame = rtc.AudioFrame.create(
                sample_rate=self.sample_rate,
                num_channels=1,
                samples_per_channel=len(beep_audio)
            )
            
            # Convert numpy array to audio frame data
            audio_frame.data[:] = (beep_audio * 32767).astype(np.int16).tobytes()
            
            await audio_source.capture_frame(audio_frame)
            
            # Wait for potential response (timeout after 5 seconds)
            await asyncio.sleep(5)
            
            # Unpublish track
            await self.room.local_participant.unpublish_track(publication.sid)
            
            # For now, simulate response detection
            # In a real implementation, this would be detected from agent audio
            simulated_response_time = time.time()
            latency_ms = (simulated_response_time - self.beep_start_time) * 1000
            
            # Log measurement
            self.log_measurement(latency_ms)
            
            return latency_ms
            
        except Exception as e:
            logger.error(f"❌ Error during beep measurement: {e}")
            return None
    
    def log_measurement(self, latency_ms):
        """Log latency measurement"""
        self.test_count += 1
        self.measurements.append(latency_ms)
        
        # Terminal output
        status = "✅ PASS" if latency_ms < 600 else "⚠️  HIGH"
        logger.info(f"🎯 Test {self.test_count}: Latency = {latency_ms:.0f}ms {status}")
        
        # Detailed log entry
        measurement_data = {
            "test_number": self.test_count,
            "timestamp": datetime.now().isoformat(),
            "latency_ms": round(latency_ms, 2),
            "status": "pass" if latency_ms < 600 else "high",
            "beep_start_time": self.beep_start_time
        }
        
        logger.info(f"📊 Measurement: {json.dumps(measurement_data)}")
    
    def calculate_statistics(self):
        """Calculate and log statistics"""
        if not self.measurements:
            logger.warning("⚠️  No measurements recorded")
            return
        
        avg_latency = statistics.mean(self.measurements)
        min_latency = min(self.measurements)
        max_latency = max(self.measurements)
        median_latency = statistics.median(self.measurements)
        
        # Count measurements under 600ms
        under_600ms = sum(1 for m in self.measurements if m < 600)
        success_rate = (under_600ms / len(self.measurements)) * 100
        
        logger.info("=" * 60)
        logger.info("📈 LATENCY MEASUREMENT SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {len(self.measurements)}")
        logger.info(f"Average Latency: {avg_latency:.1f}ms")
        logger.info(f"Minimum Latency: {min_latency:.1f}ms")
        logger.info(f"Maximum Latency: {max_latency:.1f}ms")
        logger.info(f"Median Latency: {median_latency:.1f}ms")
        logger.info(f"Tests under 600ms: {under_600ms}/{len(self.measurements)} ({success_rate:.1f}%)")
        
        if under_600ms > 0:
            logger.info("✅ SUCCESS: At least one measurement under 600ms achieved!")
        else:
            logger.warning("⚠️  WARNING: No measurements under 600ms")
        
        logger.info("=" * 60)
    
    async def run_latency_tests(self):
        """Run a series of latency tests"""
        logger.info("🚀 Starting Latency Measurement Tests")
        logger.info(f"Target: {self.max_tests} tests with latency < 600ms")
        
        # Connect to room
        if not await self.connect_to_room():
            return
        
        # Wait for agent to be ready
        logger.info("⏳ Waiting for agent to be ready...")
        await asyncio.sleep(3)
        
        # Run tests
        for i in range(self.max_tests):
            logger.info(f"🧪 Running test {i + 1}/{self.max_tests}")
            
            latency = await self.play_beep_and_measure()
            
            if latency is None:
                logger.warning(f"⚠️  Test {i + 1} failed")
                continue
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Calculate and display statistics
        self.calculate_statistics()
        
        # Disconnect
        if self.room:
            await self.room.disconnect()
            logger.info("🔌 Disconnected from room")

async def main():
    """Main function"""
    logger.info("🎯 LiveKit + Pipecat Latency Measurement Tool")
    logger.info("=" * 60)
    
    # Create measurement instance
    latency_tester = LatencyMeasurement()
    
    # Run tests
    await latency_tester.run_latency_tests()

if __name__ == "__main__":
    asyncio.run(main())
