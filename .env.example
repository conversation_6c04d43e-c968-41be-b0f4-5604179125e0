# LiveKit + Pipecat Demo Environment Variables
# Copy this file to .env and fill in your actual API keys

# OpenAI Configuration (Required)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key

# Cartesia TTS Configuration (Required)
# Get your API key from: https://cartesia.ai
CARTESIA_API_KEY=your-cartesia-api-key

# LiveKit Configuration (Optional - defaults work for local development)
LIVEKIT_URL=ws://localhost:7880
LIVEKIT_API_KEY=devkey
LIVEKIT_API_SECRET=secret

# Room Configuration (Optional)
ROOM_NAME=pipecat-demo