# LiveKit + Pipecat Demo Configuration

# LiveKit Configuration
# Option A: LiveKit Cloud (Recommended)
# LIVEKIT_URL = "wss://your-project.livekit.cloud"
# LIVEKIT_API_KEY = "your-api-key"
# LIVEKIT_API_SECRET = "your-api-secret"

# Option B: Local LiveKit Server (using docker-compose)
LIVEKIT_URL = "ws://localhost:7880"
LIVEKIT_API_KEY = "devkey"
LIVEKIT_API_SECRET = "secret"

# AI Service Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"  # Replace with your actual key

# Cartesia TTS Configuration
CARTESIA_API_KEY = "sk_car_JQ7LPAz7r8M5KuGfouEFQx"  # Add your Cartesia API key here
CARTESIA_VOICE_ID = "79a125e8-cd45-4c13-8a67-188112f4dd22"  # British Lady voice (default)

# ElevenLabs TTS Configuration (alternative to Cartesia)
ELEVENLABS_API_KEY = "your-elevenlabs-api-key"  # Add your ElevenLabs API key here
ELEVENLABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice

# Room Configuration
ROOM_NAME = "pipecat-demo"
import random
import string
AGENT_NAME = f"PipecatAgent-{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}"

# Audio Configuration
SAMPLE_RATE = 16000
CHANNELS = 1

# Agent Behavior
ECHO_SUFFIX = "...got it"
RESPONSE_TIMEOUT = 5.0  # seconds
BARGE_IN_ENABLED = True

# Logging
LOG_LEVEL = "DEBUG"  # DEBUG, INFO, WARNING, ERROR
